"""
BGE-M3 模型优化器
实现文本截断、批处理、ONNX Runtime 等优化技术
"""
import os
import time
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import gc
import psutil

# 尝试导入 ONNX Runtime
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False

# 尝试导入量化相关库
try:
    from optimum.onnxruntime import ORTModelForFeatureExtraction
    from optimum.onnxruntime.configuration import OptimizationConfig
    OPTIMUM_AVAILABLE = True
except ImportError:
    OPTIMUM_AVAILABLE = False


class BGEM3Optimizer:
    """BGE-M3 模型优化器"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        self.max_length = 8192  # BGE-M3 最大长度
        self.optimal_batch_size = 32  # 优化的批处理大小
        self.enable_onnx = ONNX_AVAILABLE
        self.enable_quantization = False
        
        # 性能统计
        self.stats = {
            "total_texts": 0,
            "total_time": 0.0,
            "truncated_texts": 0,
            "batch_count": 0,
            "avg_batch_time": 0.0
        }
        
        # 文本截断配置
        self.truncation_config = {
            "enabled": True,
            "max_length": 8192,
            "smart_truncation": True,  # 在句子边界截断
            "preserve_ratio": 0.8  # 至少保留80%的内容
        }
        
        logger.info("BGE-M3 优化器初始化完成")
        if ONNX_AVAILABLE:
            logger.info("✓ ONNX Runtime 可用")
        if OPTIMUM_AVAILABLE:
            logger.info("✓ Optimum 可用，支持模型量化")
    
    def optimize_text_length(self, text: str) -> str:
        """优化文本长度，实现智能截断"""
        try:
            if not self.truncation_config["enabled"]:
                return text
                
            if len(text) <= self.truncation_config["max_length"]:
                return text
            
            max_len = self.truncation_config["max_length"]
            
            if self.truncation_config["smart_truncation"]:
                # 智能截断：在句子边界截断
                truncated = text[:max_len]
                
                # 寻找最后一个句子结束符
                sentence_endings = ['。', '！', '？', '.', '!', '?', '\n\n']
                best_pos = -1
                min_preserve_len = int(max_len * self.truncation_config["preserve_ratio"])
                
                for ending in sentence_endings:
                    pos = truncated.rfind(ending)
                    if pos > min_preserve_len:
                        best_pos = max(best_pos, pos + 1)
                
                if best_pos > 0:
                    truncated = text[:best_pos]
                
                self.stats["truncated_texts"] += 1
                logger.debug(f"智能截断: {len(text)} -> {len(truncated)} 字符")
                return truncated
            else:
                # 简单截断
                self.stats["truncated_texts"] += 1
                return text[:max_len]
                
        except Exception as e:
            logger.error(f"文本截断失败: {e}")
            return text[:self.truncation_config["max_length"]]
    
    def optimize_batch_size(self, text_count: int, available_memory_gb: float = None) -> int:
        """根据文本数量和可用内存优化批处理大小"""
        try:
            if available_memory_gb is None:
                # 获取可用内存
                memory_info = psutil.virtual_memory()
                available_memory_gb = memory_info.available / (1024**3)
            
            # 基于内存的批大小计算
            # BGE-M3 大约需要 2GB 显存，CPU 模式下内存需求更低
            memory_based_batch = max(1, int(available_memory_gb * 8))  # 每GB内存处理8个文本
            
            # 基于文本数量的批大小
            if text_count <= 10:
                count_based_batch = text_count
            elif text_count <= 100:
                count_based_batch = 16
            else:
                count_based_batch = 32
            
            # 取较小值，确保不超过系统限制
            optimal_batch = min(
                memory_based_batch,
                count_based_batch,
                self.optimal_batch_size,
                text_count
            )
            
            logger.debug(f"优化批大小: 文本数={text_count}, 内存={available_memory_gb:.1f}GB, 批大小={optimal_batch}")
            return max(1, optimal_batch)
            
        except Exception as e:
            logger.error(f"批大小优化失败: {e}")
            return min(8, text_count)  # 回退到保守值
    
    def preprocess_texts_batch(self, texts: List[str]) -> Tuple[List[str], List[int]]:
        """批量预处理文本"""
        try:
            processed_texts = []
            original_indices = []
            
            for i, text in enumerate(texts):
                if not text or not text.strip():
                    continue
                    
                # 清理和截断文本
                cleaned_text = text.strip()
                optimized_text = self.optimize_text_length(cleaned_text)
                
                if optimized_text:
                    processed_texts.append(optimized_text)
                    original_indices.append(i)
            
            logger.debug(f"批量预处理: {len(texts)} -> {len(processed_texts)} 个有效文本")
            return processed_texts, original_indices
            
        except Exception as e:
            logger.error(f"批量预处理失败: {e}")
            return texts, list(range(len(texts)))
    
    def monitor_performance(self, batch_size: int, processing_time: float):
        """监控性能指标"""
        try:
            self.stats["batch_count"] += 1
            self.stats["total_time"] += processing_time
            self.stats["total_texts"] += batch_size
            
            # 计算平均批处理时间
            self.stats["avg_batch_time"] = self.stats["total_time"] / self.stats["batch_count"]
            
            # 内存使用监控
            memory_percent = psutil.virtual_memory().percent
            
            if processing_time > 10:  # 如果单批处理超过10秒
                logger.warning(f"批处理耗时较长: {processing_time:.2f}秒, 批大小: {batch_size}")
                
            if memory_percent > 85:
                logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
                gc.collect()  # 强制垃圾回收
                
        except Exception as e:
            logger.error(f"性能监控失败: {e}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        try:
            stats = self.stats.copy()
            
            if stats["total_texts"] > 0:
                stats["avg_time_per_text"] = stats["total_time"] / stats["total_texts"]
                stats["throughput"] = stats["total_texts"] / stats["total_time"] if stats["total_time"] > 0 else 0
                stats["truncation_rate"] = (stats["truncated_texts"] / stats["total_texts"]) * 100
            else:
                stats["avg_time_per_text"] = 0
                stats["throughput"] = 0
                stats["truncation_rate"] = 0
            
            # 添加配置信息
            stats["config"] = {
                "max_length": self.max_length,
                "optimal_batch_size": self.optimal_batch_size,
                "onnx_enabled": self.enable_onnx,
                "quantization_enabled": self.enable_quantization,
                "truncation_config": self.truncation_config
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            return {}
    
    def log_optimization_report(self):
        """输出优化报告"""
        try:
            report = self.get_optimization_report()
            
            logger.info("=== BGE-M3 优化性能报告 ===")
            logger.info(f"总处理文本数: {report.get('total_texts', 0)}")
            logger.info(f"总耗时: {report.get('total_time', 0):.2f}秒")
            logger.info(f"平均耗时: {report.get('avg_time_per_text', 0):.3f}秒/文本")
            logger.info(f"处理吞吐量: {report.get('throughput', 0):.1f} 文本/秒")
            logger.info(f"截断文本数: {report.get('truncated_texts', 0)}")
            logger.info(f"截断率: {report.get('truncation_rate', 0):.1f}%")
            logger.info(f"平均批处理时间: {report.get('avg_batch_time', 0):.2f}秒")
            
            config = report.get('config', {})
            logger.info("=== 优化配置 ===")
            logger.info(f"最大文本长度: {config.get('max_length', 'N/A')}")
            logger.info(f"批处理大小: {config.get('optimal_batch_size', 'N/A')}")
            logger.info(f"ONNX Runtime: {'启用' if config.get('onnx_enabled') else '禁用'}")
            logger.info(f"模型量化: {'启用' if config.get('quantization_enabled') else '禁用'}")
            
        except Exception as e:
            logger.error(f"输出优化报告失败: {e}")
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_texts": 0,
            "total_time": 0.0,
            "truncated_texts": 0,
            "batch_count": 0,
            "avg_batch_time": 0.0
        }
        logger.info("优化统计信息已重置")
    
    def configure_truncation(self, 
                           enabled: bool = True,
                           max_length: int = 8192,
                           smart_truncation: bool = True,
                           preserve_ratio: float = 0.8):
        """配置文本截断参数"""
        self.truncation_config.update({
            "enabled": enabled,
            "max_length": max_length,
            "smart_truncation": smart_truncation,
            "preserve_ratio": preserve_ratio
        })
        
        logger.info(f"文本截断配置已更新: 启用={enabled}, 最大长度={max_length}")
